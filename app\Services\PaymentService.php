<?php

namespace App\Services;

use App\Models\Project;
use App\Models\Transaction;
use Stripe\StripeClient;
use Stripe\Exception\ApiErrorException;
use Illuminate\Support\Facades\Log;

class PaymentService
{
    private StripeClient $stripe;
    private float $platformFeePercentage = 0.05; // 5% platform fee

    public function __construct()
    {
        $this->stripe = new StripeClient(config('services.stripe.secret'));
    }

    /**
     * Create a payment intent for escrow
     */
    public function createEscrowPayment(Project $project): array
    {
        try {
            // Check if payment already exists
            $existingTransaction = $project->transactions()
                ->where('type', 'escrow')
                ->where('status', 'completed')
                ->first();

            if ($existingTransaction) {
                return [
                    'success' => false,
                    'error' => 'Payment already completed for this project'
                ];
            }

            // Create payment intent
            $paymentIntent = $this->stripe->paymentIntents->create([
                'amount' => $this->convertToStripeAmount($project->agreed_amount),
                'currency' => config('services.stripe.currency', 'php'),
                'customer' => $project->client->stripe_customer_id,
                'metadata' => [
                    'project_id' => $project->id,
                    'type' => 'escrow',
                    'platform_fee' => $project->platform_fee,
                ],
                'automatic_payment_methods' => [
                    'enabled' => true,
                ],
            ]);

            // Create transaction record
            Transaction::create([
                'project_id' => $project->id,
                'payer_id' => $project->client_id,
                'payee_id' => $project->freelancer_id,
                'amount' => $project->agreed_amount,
                'platform_fee' => $project->platform_fee,
                'net_amount' => $project->net_amount,
                'type' => 'escrow',
                'status' => 'pending',
                'stripe_payment_intent_id' => $paymentIntent->id,
                'description' => 'Escrow payment for project #' . $project->id,
            ]);

            return [
                'success' => true,
                'client_secret' => $paymentIntent->client_secret,
                'payment_intent_id' => $paymentIntent->id
            ];
        } catch (\Exception $e) {
            Log::error('Error creating payment intent', [
                'project_id' => $project->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to create payment intent'
            ];
        }
    }

    /**
     * Confirm and process payment
     */
    public function confirmPayment(string $paymentIntentId): array
    {
        try {
            $paymentIntent = $this->stripe->paymentIntents->retrieve($paymentIntentId);
            
            if ($paymentIntent->status !== 'succeeded') {
                return ['success' => false, 'error' => 'Payment not successful'];
            }

            // Update transaction status
                $transaction = Transaction::where('stripe_payment_intent_id', $paymentIntentId)->first();
                
            if (!$transaction) {
                return ['success' => false, 'error' => 'Transaction not found'];
            }

                    $transaction->update([
                        'status' => 'completed',
                'stripe_charge_id' => $paymentIntent->charges->data[0]->id ?? null,
                'processed_at' => now(),
            ]);

            return ['success' => true];

        } catch (ApiErrorException $e) {
            Log::error('Payment confirmation failed', [
                'payment_intent_id' => $paymentIntentId,
                'error' => $e->getMessage()
            ]);

            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Release payment from escrow to freelancer
     */
    public function releasePayment(Project $project): array
    {
        try {
            $escrowTransaction = $project->transactions()
                ->where('type', 'escrow')
                ->where('status', 'completed')
                ->first();

            if (!$escrowTransaction) {
                return [
                    'success' => false,
                    'error' => 'No completed escrow payment found'
                ];
            }

            // Create transfer to freelancer's connected account
            $transfer = $this->stripe->transfers->create([
                'amount' => $this->convertToStripeAmount($project->net_amount),
                'currency' => config('services.stripe.currency', 'php'),
                'destination' => $project->freelancer->stripe_account_id,
                'source_transaction' => $escrowTransaction->stripe_charge_id,
                'metadata' => [
                    'project_id' => $project->id,
                    'escrow_transaction_id' => $escrowTransaction->id
                ]
            ]);

            // Create release transaction record
            Transaction::create([
                'project_id' => $project->id,
                'payer_id' => $project->client_id,
                'payee_id' => $project->freelancer_id,
                'amount' => $project->net_amount,
                'platform_fee' => 0,
                'net_amount' => $project->net_amount,
                'type' => 'release',
                'status' => 'completed',
                'stripe_payment_intent_id' => $transfer->id,
                'description' => 'Payment release for project #' . $project->id,
                'processed_at' => now()
            ]);

            // Update project status
            $project->update(['payment_released' => true]);

            return ['success' => true];
        } catch (\Exception $e) {
            Log::error('Error releasing payment', [
                'project_id' => $project->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to release payment'
            ];
        }
    }

    /**
     * Process refund
     */
    public function refundPayment(Project $project, string $reason): array
    {
        try {
            $escrowTransaction = $project->transactions()
                ->where('type', 'escrow')
                ->where('status', 'completed')
                ->first();

            if (!$escrowTransaction) {
                return [
                    'success' => false,
                    'error' => 'No completed escrow payment found'
                ];
            }

            // Create refund
            $refund = $this->stripe->refunds->create([
                'charge' => $escrowTransaction->stripe_charge_id,
                'reason' => $reason,
                'metadata' => [
                    'project_id' => $project->id,
                    'escrow_transaction_id' => $escrowTransaction->id
                ]
            ]);

            // Create refund transaction record
            Transaction::create([
                'project_id' => $project->id,
                'payer_id' => $project->freelancer_id,
                'payee_id' => $project->client_id,
                'amount' => $project->agreed_amount,
                'platform_fee' => 0,
                'net_amount' => $project->agreed_amount,
                'type' => 'refund',
                'status' => 'completed',
                'stripe_payment_intent_id' => $refund->id,
                'description' => 'Refund for project #' . $project->id,
                'metadata' => ['reason' => $reason],
                'processed_at' => now()
            ]);

            return ['success' => true];
        } catch (\Exception $e) {
            Log::error('Error refunding payment', [
                'project_id' => $project->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => 'Failed to refund payment'
            ];
        }
    }

    /**
     * Get payment history for user
     */
    public function getPaymentHistory(int $userId, int $limit = 50): array
    {
        $transactions = Transaction::where(function($query) use ($userId) {
            $query->where('payer_id', $userId)
                  ->orWhere('payee_id', $userId);
        })
        ->with(['project.job', 'payer', 'payee'])
        ->orderBy('created_at', 'desc')
        ->limit($limit)
        ->get();

        return $transactions->map(function($transaction) use ($userId) {
            return [
                'id' => $transaction->id,
                'project_title' => $transaction->project->job->title ?? 'N/A',
                'amount' => (float) $transaction->amount,
                'net_amount' => (float) $transaction->net_amount,
                'platform_fee' => (float) $transaction->platform_fee,
                'type' => $transaction->type,
                'status' => $transaction->status,
                'description' => $transaction->description,
                'is_incoming' => $transaction->payee_id === $userId,
                'other_party' => $transaction->payee_id === $userId 
                    ? $transaction->payer->first_name . ' ' . $transaction->payer->last_name
                    : $transaction->payee->first_name . ' ' . $transaction->payee->last_name,
                'date' => $transaction->created_at->format('M d, Y'),
                'processed_at' => $transaction->processed_at?->format('M d, Y H:i')
            ];
        })->toArray();
    }

    /**
     * Get demo test cards for presentation
     */
    public function getTestCards(): array
    {
        return [
            [
                'number' => '****************',
                'description' => 'Succeeds and immediately processes the payment',
            ],
            [
                'number' => '****************',
                'description' => 'Requires authentication',
            ],
            [
                'number' => '****************',
                'description' => 'Declined payment',
            ],
        ];
    }

    /**
     * Calculate platform fee
     */
    private function calculatePlatformFee(float $amount): float
    {
        return round($amount * $this->platformFeePercentage, 2);
    }

    /**
     * Convert amount to Stripe format (cents)
     */
    private function convertToStripeAmount(float $amount): int
    {
        return (int) ($amount * 100);
    }
}
